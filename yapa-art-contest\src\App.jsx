import React, { useState } from 'react'
import './App.css'

// Sample data - replace with real values
const CONTEST_DATA = {
  startDate: "January 15, 2025",
  endDate: "January 22, 2025",
  theme: "Cosmic Odyssey: Journey Through the Stars",
  discordInvite: "https://discord.gg/8RbVtGRF",
  timezone: "IST (Asia/Kolkata)"
}

const JUDGES = [
  { name: "<PERSON>", role: "Digital Artist & UI Designer", avatar: "👨‍🎨" },
  { name: "<PERSON>", role: "Concept Artist & Illustrator", avatar: "👩‍🎨" },
  { name: "<PERSON>", role: "3D Artist & Animation Director", avatar: "🎭" }
]

const SAMPLE_FINALISTS = [
  { id: 1, title: "Nebula's Heart", artist: "StarGazer", votes: 42, thumbnail: "🌌" },
  { id: 2, title: "Galaxy Spiral", artist: "CosmicArtist", votes: 38, thumbnail: "🌀" },
  { id: 3, title: "Stellar Birth", artist: "<PERSON><PERSON><PERSON>ain<PERSON>", votes: 35, thumbnail: "⭐" }
]

function App() {
  const [formData, setFormData] = useState({
    discordUsername: '',
    realName: '',
    artworkTitle: '',
    artistStatement: '',
    portfolioLink: '',
    consent: false
  })

  const [scrollY, setScrollY] = useState(0)

  React.useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    alert('Submission received! You will receive a confirmation email shortly.')
    // Here you would typically send the data to your backend
  }

  return (
    <div className="app">
      {/* Navigation */}
      <nav className="nav">
        <div className="nav-container">
          <div className="nav-logo">YAPA YAPA</div>
          <div className="nav-links">
            <a href="#how-it-works">How It Works</a>
            <a href="#timeline">Timeline</a>
            <a href="#submit">Submit</a>
            <a href="#gallery">Gallery</a>
            <a href="#rules">Rules</a>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <header className="hero">
        <div className="stars-bg" style={{ transform: `translateY(${scrollY * 0.5}px)` }}></div>
        <div className="nebula-bg" style={{ transform: `translateY(${scrollY * 0.3}px)` }}></div>
        <div className="planets-bg" style={{ transform: `translateY(${scrollY * 0.7}px)` }}></div>
        <div className="hero-content" style={{ transform: `translateY(${scrollY * 0.2}px)` }}>
          <h1 className="hero-title">
            <span className="title-main">YAPA YAPA</span>
            <span className="title-sub">COSMIC ART CONTEST</span>
          </h1>
          <p className="hero-subtitle">
            Journey through the cosmos — 2 rounds • 1 week • stellar judging • galactic community final
          </p>
          <p className="hero-description">
            Explore the infinite universe through your art. Create cosmic masterpieces, compete for stellar prizes,
            and earn your place among the stars in our community constellation.
          </p>
          <div className="hero-buttons">
            <a href={CONTEST_DATA.discordInvite} className="btn btn-primary cosmic-btn" target="_blank" rel="noopener noreferrer">
              🚀 Join the Discord
            </a>
            <a href="#submit" className="btn btn-secondary cosmic-btn">
              ⭐ Submit Entry
            </a>
          </div>
        </div>
        <div className="shooting-stars">
          <div className="shooting-star"></div>
          <div className="shooting-star"></div>
          <div className="shooting-star"></div>
        </div>
      </header>

      {/* Theme Announcement */}
      <section className="theme-section">
        <div className="cosmic-particles" style={{ transform: `translateY(${scrollY * 0.1}px)` }}></div>
        <div className="container">
          <div className="theme-card cosmic-card">
            <div className="cosmic-border"></div>
            <h2>🌌 Contest Theme</h2>
            <div className="theme-title">{CONTEST_DATA.theme}</div>
            <p>Embark on an artistic journey through the cosmos! Create stunning visuals inspired by galaxies, nebulae,
               black holes, stellar formations, and the infinite mysteries of space. Let the universe be your canvas!</p>
            <div className="theme-elements">
              <span className="theme-tag">🌟 Stars & Galaxies</span>
              <span className="theme-tag">🌌 Nebulae & Cosmic Dust</span>
              <span className="theme-tag">🕳️ Black Holes & Wormholes</span>
              <span className="theme-tag">🪐 Planets & Moons</span>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="section">
        <div className="container">
          <h2 className="section-title">How It Works</h2>
          <div className="how-it-works-grid">
            <div className="work-card cosmic-work-card" style={{ transform: `translateY(${scrollY * 0.05}px)` }}>
              <div className="work-icon">🌌</div>
              <h3>2 Cosmic Rounds</h3>
              <p>Round 1: Stellar judges select cosmic finalists<br/>Round 2: Galaxy community votes for the ultimate winner</p>
            </div>
            <div className="work-card cosmic-work-card" style={{ transform: `translateY(${scrollY * 0.03}px)` }}>
              <div className="work-icon">⏰</div>
              <h3>1 Week Odyssey</h3>
              <p>{CONTEST_DATA.startDate} → {CONTEST_DATA.endDate}</p>
            </div>
            <div className="work-card cosmic-work-card" style={{ transform: `translateY(${scrollY * 0.07}px)` }}>
              <div className="work-icon">⭐</div>
              <h3>Galactic Voting</h3>
              <p>Final cosmic champion determined by star reactions on the official finalists constellation</p>
            </div>
            <div className="work-card cosmic-work-card" style={{ transform: `translateY(${scrollY * 0.04}px)` }}>
              <div className="work-icon">🎨</div>
              <h3>One Stellar Entry</h3>
              <p>Submit your cosmic masterpiece - make it shine across the universe!</p>
            </div>
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section id="timeline" className="section timeline-section">
        <div className="container">
          <h2 className="section-title">Contest Timeline</h2>
          <p className="timezone-note">All times in {CONTEST_DATA.timezone}</p>
          <div className="timeline">
            <div className="timeline-item">
              <div className="timeline-marker">0</div>
              <div className="timeline-content">
                <h3>Launch Day</h3>
                <p>Theme reveal + rules published</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-marker">1-3</div>
              <div className="timeline-content">
                <h3>Submissions Open</h3>
                <p>Participants upload their amazing entries</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-marker">4</div>
              <div className="timeline-content">
                <h3>Judges Review</h3>
                <p>Our expert panel selects the finalists</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-marker">5-6</div>
              <div className="timeline-content">
                <h3>Community Voting</h3>
                <p>Finalists displayed publicly; server members vote by ticking</p>
              </div>
            </div>
            <div className="timeline-item">
              <div className="timeline-marker">7</div>
              <div className="timeline-content">
                <h3>Winner Announcement</h3>
                <p>Results published, rewards distributed, roles updated</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Submission Form */}
      <section id="submit" className="section submission-section">
        <div className="container">
          <h2 className="section-title">Submit Your Entry</h2>
          <div className="submission-requirements">
            <h3>Submission Requirements</h3>
            <ul>
              <li><strong>File formats:</strong> PNG, JPG/JPEG (flattened). Optional: attach layered file on request</li>
              <li><strong>Max file size:</strong> 20 MB</li>
              <li><strong>Naming convention:</strong> Round1_[DiscordName]_[Title].png</li>
              <li><strong>Originality:</strong> Must be created by you. Include attribution for references/stock used</li>
            </ul>
          </div>

          <form className="submission-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="discordUsername">Discord Username and Tag *</label>
              <input
                type="text"
                id="discordUsername"
                name="discordUsername"
                placeholder="e.g., ArtistName#1234"
                value={formData.discordUsername}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="realName">Real Name (Optional)</label>
              <input
                type="text"
                id="realName"
                name="realName"
                value={formData.realName}
                onChange={handleInputChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="artworkTitle">Title of Artwork *</label>
              <input
                type="text"
                id="artworkTitle"
                name="artworkTitle"
                value={formData.artworkTitle}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="artistStatement">Artist Statement (1-2 sentences) *</label>
              <textarea
                id="artistStatement"
                name="artistStatement"
                placeholder="Describe your inspiration or process..."
                value={formData.artistStatement}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="artworkFile">Upload Artwork (PNG/JPEG) *</label>
              <input
                type="file"
                id="artworkFile"
                name="artworkFile"
                accept=".png,.jpg,.jpeg"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="layeredFile">Upload Layered/Source File (Optional)</label>
              <input
                type="file"
                id="layeredFile"
                name="layeredFile"
              />
            </div>

            <div className="form-group">
              <label htmlFor="portfolioLink">Portfolio/Social Links (Optional)</label>
              <input
                type="url"
                id="portfolioLink"
                name="portfolioLink"
                placeholder="https://..."
                value={formData.portfolioLink}
                onChange={handleInputChange}
              />
            </div>

            <div className="form-group checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="consent"
                  checked={formData.consent}
                  onChange={handleInputChange}
                  required
                />
                <span className="checkmark"></span>
                I grant Yapa Yapa permission to feature my work for promotion (non-commercial) with credit. *
              </label>
            </div>

            <button type="submit" className="btn btn-primary btn-submit">
              Submit Entry
            </button>
          </form>
        </div>
      </section>

      {/* Gallery/Finalists */}
      <section id="gallery" className="section gallery-section">
        <div className="container">
          <h2 className="section-title">Finalists Gallery</h2>
          <p className="gallery-description">
            Vote for your favorite by reacting with ✅ on Discord!
          </p>

          <div className="finalists-grid">
            {SAMPLE_FINALISTS.map(finalist => (
              <div key={finalist.id} className="finalist-card">
                <div className="finalist-thumbnail">{finalist.thumbnail}</div>
                <h3>{finalist.title}</h3>
                <p className="finalist-artist">by {finalist.artist}</p>
                <div className="finalist-votes">
                  <span className="vote-count">{finalist.votes}</span>
                  <span className="vote-label">votes</span>
                </div>
              </div>
            ))}
          </div>

          <div className="vote-cta">
            <a href={CONTEST_DATA.discordInvite} className="btn btn-primary" target="_blank" rel="noopener noreferrer">
              Vote on Discord
            </a>
          </div>
        </div>
      </section>

      {/* Prizes & Rewards */}
      <section className="section prizes-section">
        <div className="container">
          <h2 className="section-title">Prizes & Rewards</h2>
          <div className="prizes-grid">
            <div className="prize-card winner">
              <div className="prize-icon">🏆</div>
              <h3>Winner</h3>
              <ul>
                <li>Featured server banner slot</li>
                <li>Custom winner role</li>
                <li>$100 Gift Card</li>
                <li>Commissioned emote of their work</li>
              </ul>
            </div>
            <div className="prize-card runner-up">
              <div className="prize-icon">🥈</div>
              <h3>Runner-up</h3>
              <ul>
                <li>Featured in server showcase</li>
                <li>Special runner-up role</li>
                <li>$50 Gift Card</li>
              </ul>
            </div>
            <div className="prize-card finalist">
              <div className="prize-icon">🎨</div>
              <h3>All Finalists</h3>
              <ul>
                <li>Finalist role</li>
                <li>Inclusion in downloadable gallery</li>
                <li>Certificate of recognition</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Rules & Guidelines */}
      <section id="rules" className="section rules-section">
        <div className="container">
          <h2 className="section-title">Rules & Guidelines</h2>
          <div className="rules-content">
            <div className="rules-column">
              <h3>Submission Rules</h3>
              <ul>
                <li>One entry per person</li>
                <li>Entries must be original (or clearly cite references)</li>
                <li>No NSFW / hate symbols / copyrighted fan art</li>
                <li>Follow server standard content policy</li>
              </ul>
            </div>
            <div className="rules-column">
              <h3>Judging & Voting</h3>
              <ul>
                <li>Judges' decisions for Round 1 are final</li>
                <li>Community vote decides Round 2 winner</li>
                <li>Tie-breaker: judges cast deciding votes</li>
                <li>Transparency in finalist selection</li>
              </ul>
            </div>
            <div className="rules-column">
              <h3>Usage Rights</h3>
              <ul>
                <li>Artists allow non-commercial promotion with credit</li>
                <li>All other rights remain with the artist</li>
                <li>Work may be featured in server content</li>
                <li>Attribution always provided</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Judges & Organizers */}
      <section className="section judges-section">
        <div className="container">
          <h2 className="section-title">Meet Our Judges</h2>
          <div className="judges-grid">
            {JUDGES.map((judge, index) => (
              <div key={index} className="judge-card">
                <div className="judge-avatar">{judge.avatar}</div>
                <h3>{judge.name}</h3>
                <p>{judge.role}</p>
              </div>
            ))}
          </div>
          <div className="organizers-contact">
            <h3>Need Help?</h3>
            <p>Contact our moderators on Discord: @ModeratorName, @AnotherMod</p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>Yapa Yapa Art Contest</h3>
              <p>Celebrating creativity in our amazing community</p>
            </div>
            <div className="footer-section">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="#how-it-works">How It Works</a></li>
                <li><a href="#submit">Submit Entry</a></li>
                <li><a href="#rules">Rules</a></li>
                <li><a href="#gallery">Gallery</a></li>
              </ul>
            </div>
            <div className="footer-section">
              <h3>Connect</h3>
              <a href={CONTEST_DATA.discordInvite} className="discord-link" target="_blank" rel="noopener noreferrer">
                Join our Discord
              </a>
            </div>
          </div>
          <div className="footer-bottom">
            <p>
              By submitting, you grant Yapa Yapa permission to feature your work for non-commercial
              promotion with attribution. All other rights remain with the artist.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
