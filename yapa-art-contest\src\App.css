/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #ff6b6b;
  --secondary-color: #4ecdc4;
  --accent-color: #ffe66d;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.2);
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: var(--dark-color);
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.nav {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  box-shadow: var(--shadow-light);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--dark-color);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-links a:hover {
  color: var(--primary-color);
}

.nav-links a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: width 0.3s ease;
}

.nav-links a:hover::after {
  width: 100%;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.9;
  z-index: -2;
}

.hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.15"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 2rem;
}

.hero-title {
  margin-bottom: 1.5rem;
}

.title-main {
  display: block;
  font-family: 'Playfair Display', serif;
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -2px;
}

.title-sub {
  display: block;
  font-family: 'Inter', sans-serif;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 300;
  color: var(--accent-color);
  letter-spacing: 8px;
  margin-top: -10px;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  font-weight: 400;
}

.hero-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: var(--dark-color);
  transform: translateY(-2px);
}

/* Theme Section */
.theme-section {
  padding: 4rem 0;
  background: var(--light-color);
}

.theme-card {
  background: white;
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.theme-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-accent);
}

.theme-card h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.theme-title {
  font-family: 'Playfair Display', serif;
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

/* Sections */
.section {
  padding: 5rem 0;
}

.section-title {
  font-family: 'Playfair Display', serif;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: var(--dark-color);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

/* How It Works */
.how-it-works-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.work-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.work-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.work-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.work-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.work-card p {
  color: #666;
  line-height: 1.6;
}

/* Timeline */
.timeline-section {
  background: var(--light-color);
}

.timezone-note {
  text-align: center;
  color: #666;
  margin-bottom: 3rem;
  font-style: italic;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--gradient-primary);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-marker {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: var(--gradient-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  font-size: 1.1rem;
  box-shadow: var(--shadow-medium);
  z-index: 2;
}

.timeline-content {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  width: calc(50% - 50px);
  position: relative;
}

.timeline-item:nth-child(odd) .timeline-content {
  margin-right: auto;
}

.timeline-item:nth-child(even) .timeline-content {
  margin-left: auto;
}

.timeline-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.timeline-content p {
  color: #666;
  line-height: 1.6;
}

/* Submission Form */
.submission-section {
  background: white;
}

.submission-requirements {
  background: var(--light-color);
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 3rem;
}

.submission-requirements h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.submission-requirements ul {
  list-style: none;
  padding: 0;
}

.submission-requirements li {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.submission-requirements li:last-child {
  border-bottom: none;
}

.submission-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.checkbox-group {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: 400 !important;
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.btn-submit {
  width: 100%;
  font-size: 1.1rem;
  padding: 1.25rem;
  margin-top: 1rem;
}

/* Gallery */
.gallery-section {
  background: var(--light-color);
}

.gallery-description {
  text-align: center;
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 3rem;
}

.finalists-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.finalist-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.finalist-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.finalist-thumbnail {
  height: 200px;
  background: var(--gradient-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
}

.finalist-card h3 {
  padding: 1rem 1.5rem 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark-color);
}

.finalist-artist {
  padding: 0 1.5rem;
  color: #666;
  font-style: italic;
}

.finalist-votes {
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.vote-count {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.vote-label {
  color: #666;
}

.vote-cta {
  text-align: center;
}

/* Prizes */
.prizes-section {
  background: white;
}

.prizes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.prize-card {
  background: var(--light-color);
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.prize-card:hover {
  transform: translateY(-5px);
}

.prize-card.winner {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: var(--dark-color);
}

.prize-card.runner-up {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: var(--dark-color);
}

.prize-card.finalist {
  background: linear-gradient(135deg, #cd7f32 0%, #deb887 100%);
  color: var(--dark-color);
}

.prize-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.prize-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.prize-card ul {
  list-style: none;
  padding: 0;
  text-align: left;
}

.prize-card li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.prize-card li::before {
  content: '✨';
  position: absolute;
  left: 0;
  top: 0.5rem;
}

/* Rules */
.rules-section {
  background: var(--light-color);
}

.rules-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.rules-column {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
}

.rules-column h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.rules-column ul {
  list-style: none;
  padding: 0;
}

.rules-column li {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  padding-left: 1.5rem;
}

.rules-column li:last-child {
  border-bottom: none;
}

.rules-column li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Judges */
.judges-section {
  background: white;
}

.judges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.judge-card {
  background: var(--light-color);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  transition: transform 0.3s ease;
}

.judge-card:hover {
  transform: translateY(-5px);
}

.judge-avatar {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.judge-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.judge-card p {
  color: #666;
  font-style: italic;
}

.organizers-contact {
  text-align: center;
  background: var(--gradient-primary);
  color: white;
  padding: 2rem;
  border-radius: 15px;
}

.organizers-contact h3 {
  margin-bottom: 1rem;
}

/* Footer */
.footer {
  background: var(--dark-color);
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--accent-color);
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--accent-color);
}

.discord-link {
  display: inline-block;
  background: var(--gradient-secondary);
  color: white !important;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: transform 0.3s ease;
}

.discord-link:hover {
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 2rem;
  text-align: center;
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .nav-links a {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 80px;
  }

  .timeline-marker {
    left: 30px !important;
    transform: none !important;
  }

  .timeline-content {
    width: 100% !important;
    margin: 0 !important;
  }

  .section {
    padding: 3rem 0;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .title-main {
    font-size: 2.5rem;
  }

  .title-sub {
    font-size: 1.2rem;
    letter-spacing: 4px;
  }

  .section-title {
    font-size: 2rem;
  }

  .how-it-works-grid,
  .prizes-grid,
  .rules-content,
  .judges-grid {
    grid-template-columns: 1fr;
  }
}
